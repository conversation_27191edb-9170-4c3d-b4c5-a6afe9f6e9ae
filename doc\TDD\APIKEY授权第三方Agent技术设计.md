# APIKEY授权第三方Agent技术设计

## 一、总体架构
APIKEY授权体系与现有JWT认证体系无缝集成，认证流程、权限注入、数据隔离、接口调用方式与JWT完全一致。APIKEY与userId强绑定，认证后注入用户上下文，权限体系完全复用。

## 二、数据表设计
表名：base_sys_apikey
| 字段名      | 类型         | 说明                   |
|-------------|--------------|------------------------|
| id          | bigint       | 主键                   |
| user_id     | bigint       | 关联用户ID             |
| apikey      | varchar(64)  | APIKEY（唯一、加密存储）|
| status      | int          | 状态（0禁用/1启用）    |
| expire_time | datetime     | 过期时间（null为永久） |
| remark      | varchar(255) | 备注                   |
| create_time | datetime     | 创建时间               |

## 三、APIKEY格式与认证分流
- APIKEY明文统一以`CAK_`前缀开头，前端页面需完整展示和复制。
- 拦截器认证分流逻辑：
  - 以CAK_开头：APIKEY认证（去前缀后SHA256查表、校验、注入上下文）
  - 三段.分隔：JWT认证
  - 其他：直接拒绝，非法token
- APIKEY生成、重置、查找等Service方法均自动处理CAK_前缀。

## 四、APIKEY认证与JWT认证共用拦截器
- 统一使用Authorization: Bearer {token}请求头。
- 拦截器收到请求后，优先判断CAK_前缀，分流APIKEY或JWT认证。
- 权限体系、数据隔离、接口调用方式全部与JWT用户一致。

## 五、权限复用与上下文注入
- APIKEY与userId强绑定，权限体系、数据隔离、审计全部一致。
- 认证通过后，注入用户上下文，后续业务代码无感知。
- 支持多key并行，便于分用途、分Agent授权。

## 六、前端页面结构
- 用户管理详情页新增"APIKEY管理"Tab。
- 支持新建、禁用、重置、删除APIKEY，查看调用日志。
- 前端/第三方调用方式与JWT完全一致，无需特殊适配。
- 前端页面需完整展示和复制CAK_前缀，防止用户误删。

## 七、接口设计
- POST /admin/user/{userId}/apikey/create  创建APIKEY
- POST /admin/user/{userId}/apikey/{keyId}/reset  重置APIKEY
- POST /admin/user/{userId}/apikey/{keyId}/disable  禁用APIKEY
- DELETE /admin/user/{userId}/apikey/{keyId}  删除APIKEY
- GET  /admin/user/{userId}/apikey/list  查询APIKEY列表
- GET  /admin/user/{userId}/apikey/{keyId}/log  查询调用日志

## 八、调用流程
1. 管理员为userId生成APIKEY，分发给第三方Agent。
2. Agent调用接口时统一用Authorization: Bearer {APIKEY}，APIKEY需带CAK_前缀。
3. 后端拦截器自动分流认证，查找用户、加载权限，注入上下文。
4. 权限、数据隔离、审计全部复用现有体系。

## 九、扩展建议
- 支持APIKEY调用频率限制、IP白名单、调用统计等。
- 支持APIKEY细粒度接口/数据授权。 