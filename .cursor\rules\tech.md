# Cool Admin Technology Stack

## Backend (Java)

### Core Framework
- **Spring Boot**: 3.2.5 (Java 17+)
- **Spring Security**: Authentication and authorization
- **Spring Data**: Data access layer
- **Spring Quartz**: Task scheduling
- **Spring Cache**: Caching abstraction

### Database & ORM
- **MyBatis-Flex**: 1.10.9 - Modern MyBatis enhancement
- **AutoTable**: Automatic table generation from entities
- **MySQL/PostgreSQL**: Primary databases
- **Redis**: Caching and session storage
- **HikariCP**: Connection pooling

### Build & Dependencies
- **Maven**: Dependency management and build tool
- **Lombok**: Code generation for boilerplate reduction
- **Hutool**: Java utility library
- **FastJSON2**: JSON processing
- **SpringDoc OpenAPI**: API documentation

### AI Integration
- **OpenAI GPT**: AI model integration
- **Dify**: Workflow orchestration platform
- **Custom AI Services**: Task generation, SOP parsing, quality checks

## Frontend (Vue.js)

### Core Framework
- **Vue 3**: Composition API with TypeScript
- **Vite**: Build tool and dev server
- **Element Plus**: UI component library
- **Vue Router 4**: Client-side routing
- **Pinia**: State management

### Development Tools
- **TypeScript**: Type safety
- **SCSS**: CSS preprocessing
- **ESLint + Prettier**: Code formatting and linting
- **Vue DevTools**: Development debugging

### Cool Framework Features
- **@cool-vue/crud**: CRUD component system
- **EPS System**: Automatic service generation
- **Plugin Architecture**: Modular plugin system
- **Real-time Updates**: SSE integration

## Common Commands

### Backend (Java)
```bash
# Development
mvn spring-boot:run

# Build
mvn clean package

# Test
mvn test

# Generate tables (AutoTable)
# Tables are auto-generated from entity classes on startup
```

### Frontend (Vue)
```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Build for production
pnpm build

# Build for demo
pnpm build-demo

# Type checking
pnpm type-check

# Linting
pnpm lint

# Format code
pnpm format
```

## Development Ports
- **Backend**: 18001 (configurable via SERVER_PORT)
- **Frontend**: 19000
- **API Documentation**: http://localhost:18001/swagger

## Key Configuration Files
- **Backend**: `application.yml`, `application-local.yml`, `application-prod.yml`
- **Frontend**: `vite.config.ts`, `src/config/dev.ts`, `src/config/prod.ts`
- **Build**: `pom.xml` (Maven), `package.json` (npm/pnpm)