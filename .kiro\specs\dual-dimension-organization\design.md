# 双维度组织架构设计文档

## 概述

双维度组织架构系统通过支持部门维度和项目维度的并行管理，实现矩阵式组织管理模式。系统保持现有部门权限体系不变，新增独立的项目维度组织架构，用户可在两种组织形态间自由切换。

## 架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    双维度组织架构系统                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue3 + TypeScript)                                 │
│  ├── 组织形态切换器                                          │
│  ├── 动态菜单系统                                            │
│  ├── 部门维度界面                                            │
│  └── 项目维度界面                                            │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Spring Boot)                                       │
│  ├── 组织形态管理服务                                        │
│  ├── 双维度权限服务                                          │
│  ├── 项目管理服务                                            │
│  └── 用户组织关系服务                                        │
├─────────────────────────────────────────────────────────────┤
│  数据层 (MySQL + Redis)                                     │
│  ├── 用户组织关系表                                          │
│  ├── 项目信息表                                              │
│  ├── 用户当前模式表                                          │
│  └── 权限缓存                                                │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则

1. **兼容性优先**：保持现有部门权限体系100%不变
2. **模块化设计**：项目维度作为独立模块，可插拔
3. **性能优化**：多层缓存机制，优化权限查询性能
4. **扩展性**：支持未来更多组织维度的扩展

## 组件和接口

### 后端核心组件

#### 1. 组织形态枚举
```java
public enum OrganizationModeEnum {
    DEPARTMENT("DEPARTMENT", "部门维度"),
    PROJECT("PROJECT", "项目维度");
    
    private final String code;
    private final String name;
}
```

#### 2. 全局项目角色枚举
```java
public enum GlobalProjectRoleEnum {
    PROJECT_OWNER("PROJECT_OWNER", "项目负责人", 4),
    PROJECT_ADMIN("PROJECT_ADMIN", "项目管理员", 3),
    PROJECT_MEMBER("PROJECT_MEMBER", "项目成员", 2),
    PROJECT_VIEWER("PROJECT_VIEWER", "项目观察者", 1);
    
    private final String code;
    private final String name;
    private final Integer level;
}
```

#### 3. 组织形态管理服务
```java
@Service
public class OrganizationModeService {
    
    /**
     * 获取用户当前组织形态
     */
    String getCurrentMode(Long userId);
    
    /**
     * 切换用户组织形态
     */
    void switchMode(Long userId, String targetMode);
    
    /**
     * 验证用户是否可以切换到指定组织形态
     */
    boolean canSwitchToMode(Long userId, String targetMode);
    
    /**
     * 获取用户可用的组织形态列表
     */
    List<String> getAvailableModes(Long userId);
}
```

#### 4. 双维度权限服务
```java
@Service
public class DualDimensionPermissionService {
    
    /**
     * 检查用户权限
     */
    boolean hasPermission(Long userId, String permission, Map<String, Object> context);
    
    /**
     * 获取用户菜单权限
     */
    Dict getPermissionMenu(Long userId);
    
    /**
     * 获取用户在当前组织形态下的角色列表
     */
    List<String> getUserRoles(Long userId);
}
```

#### 5. 项目管理服务
```java
@Service
public class ProjectManagementService {
    
    /**
     * 获取用户参与的项目列表
     */
    List<ProjectInfo> getUserProjects(Long userId);
    
    /**
     * 创建项目
     */
    ProjectInfo createProject(ProjectCreateRequest request);
    
    /**
     * 添加项目成员
     */
    void addProjectMember(Long projectId, Long userId, String roleCode);
    
    /**
     * 移除项目成员
     */
    void removeProjectMember(Long projectId, Long userId);
    
    /**
     * 获取项目成员列表
     */
    List<ProjectMember> getProjectMembers(Long projectId);
}
```

### 前端核心组件

#### 1. 组织状态管理 (Pinia Store)
```typescript
interface OrganizationState {
  currentMode: string;
  availableModes: string[];
  userProjects: Array<{
    id: number;
    name: string;
    role: string;
    canManage: boolean;
  }>;
}

export const useOrganizationStore = defineStore('organization', {
  state: (): OrganizationState => ({
    currentMode: 'DEPARTMENT',
    availableModes: [],
    userProjects: []
  }),
  
  actions: {
    async switchMode(targetMode: string),
    async loadOrganizationInfo(),
    async refreshMenuAndPermissions()
  }
});
```

#### 2. 组织形态切换器组件
```vue
<template>
  <el-select 
    v-model="orgStore.currentMode" 
    @change="handleModeSwitch"
    class="org-mode-switcher"
  >
    <el-option 
      v-for="mode in orgStore.availableModes"
      :key="mode"
      :label="getModeLabel(mode)"
      :value="mode"
    />
  </el-select>
</template>
```

#### 3. 项目管理页面组件
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn v-if="canCreateProject" />
      <cl-flex1 />
      <cl-search-key />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table">
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="userRole" label="我的角色">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.userRole)">
              {{ getRoleLabel(row.userRole) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button size="small" @click="viewProject(row)">查看</el-button>
            <el-button 
              v-if="row.canManage" 
              size="small" 
              type="primary" 
              @click="editProject(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.canDelete" 
              size="small" 
              type="danger" 
              @click="deleteProject(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </cl-table>
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>
```

### API接口设计

#### 1. 组织形态管理接口
```http
GET /admin/organization/mode/current
POST /admin/organization/mode/switch
GET /admin/organization/mode/available
```

#### 2. 项目管理接口
```http
GET /admin/project/user/list
POST /admin/project/create
PUT /admin/project/{id}
DELETE /admin/project/{id}
GET /admin/project/{id}/members
POST /admin/project/{id}/members
DELETE /admin/project/{id}/members/{userId}
```

#### 3. 权限验证接口
```http
GET /admin/permission/check
GET /admin/base/comm/permmenu (扩展现有接口)
```

## 数据模型

### 核心数据表

#### 1. 用户组织关系表 (user_organization)
```sql
CREATE TABLE `user_organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `organization_type` varchar(20) NOT NULL COMMENT '组织类型：DEPARTMENT/PROJECT',
  `organization_id` bigint NOT NULL COMMENT '组织ID（部门ID或项目ID）',
  `role_code` varchar(50) NOT NULL COMMENT '角色代码',
  `is_primary` tinyint DEFAULT '0' COMMENT '是否主要组织：0-否 1-是',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `expire_time` datetime DEFAULT NULL COMMENT '权限到期时间',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-正常 2-暂停 3-已移除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_org` (`user_id`, `organization_type`, `organization_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_org_type_id` (`organization_type`, `organization_id`)
) COMMENT='用户组织关系表';
```

#### 2. 项目信息表 (project_info)
```sql
CREATE TABLE `project_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `project_code` varchar(100) NOT NULL COMMENT '项目编码',
  `description` text COMMENT '项目描述',
  `owner_id` bigint NOT NULL COMMENT '项目负责人ID',
  `status` tinyint DEFAULT '1' COMMENT '项目状态：1-进行中 2-已完成 3-已暂停 4-已取消',
  `priority` tinyint DEFAULT '3' COMMENT '项目优先级：1-低 2-普通 3-中等 4-高 5-紧急',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `tags` json DEFAULT NULL COMMENT '项目标签',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`)
) COMMENT='项目信息表';
```

#### 3. 用户当前组织形态表 (user_current_mode)
```sql
CREATE TABLE `user_current_mode` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `current_mode` varchar(20) NOT NULL COMMENT '当前组织形态：DEPARTMENT/PROJECT',
  `last_switch_time` datetime NOT NULL COMMENT '最后切换时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) COMMENT='用户当前组织形态表';
```

### 数据关系

```
用户表 (base_sys_user)
    ↓ 1:N
用户组织关系表 (user_organization)
    ↓ N:1
组织表 (base_sys_dept / project_info)

用户表 (base_sys_user)
    ↓ 1:1
用户当前模式表 (user_current_mode)
```

## 错误处理

### 异常类型定义

1. **组织形态切换异常**
   - 无效的组织形态
   - 用户无权限切换到目标形态
   - 切换过程中的系统错误

2. **权限验证异常**
   - 用户权限不足
   - 权限计算错误
   - 权限缓存异常

3. **项目管理异常**
   - 项目不存在
   - 用户不是项目成员
   - 项目状态不允许操作

### 错误处理策略

```java
@ControllerAdvice
public class DualDimensionExceptionHandler {
    
    @ExceptionHandler(OrganizationModeException.class)
    public R handleOrganizationModeException(OrganizationModeException e) {
        return R.error(e.getMessage());
    }
    
    @ExceptionHandler(PermissionDeniedException.class)
    public R handlePermissionDeniedException(PermissionDeniedException e) {
        return R.error("权限不足：" + e.getMessage());
    }
    
    @ExceptionHandler(ProjectManagementException.class)
    public R handleProjectManagementException(ProjectManagementException e) {
        return R.error("项目操作失败：" + e.getMessage());
    }
}
```

## 测试策略

### 单元测试

1. **权限计算测试**
   - 部门维度权限计算
   - 项目维度权限计算
   - 权限缓存机制测试

2. **组织形态切换测试**
   - 有效切换场景
   - 无效切换场景
   - 权限验证测试

3. **项目管理测试**
   - 项目CRUD操作
   - 成员管理操作
   - 角色权限验证

### 集成测试

1. **API接口测试**
   - 组织形态管理接口
   - 项目管理接口
   - 权限验证接口

2. **前后端集成测试**
   - 菜单动态加载
   - 权限实时验证
   - 数据权限过滤

3. **数据库事务测试**
   - 组织关系变更事务
   - 权限缓存一致性
   - 数据迁移验证

### 性能测试

1. **权限查询性能**
   - 单用户权限查询 < 500ms
   - 批量权限查询优化
   - 缓存命中率 > 90%

2. **组织形态切换性能**
   - 切换响应时间 < 2秒
   - 菜单加载时间 < 1秒
   - 并发切换测试

3. **系统负载测试**
   - 1000+并发用户支持
   - 数据库连接池优化
   - Redis缓存性能

## 优化的项目工作台菜单结构

### 菜单层级设计

```
项目工作台
├── 项目概览 (/project/dashboard)
│   └── 显示项目统计、进度概览、任务分布等仪表板信息
├── 项目管理 (/project/management)
│   └── 显示用户参与的所有项目，标识角色和管理权限
├── 成员管理 (/project/member)
│   └── 管理当前选中项目的成员和角色分配
├── 任务管理 (/project/task)
│   └── 管理项目相关的任务信息
└── 项目报表 (/project/report)
    └── 显示项目统计和分析报表
```

### 项目管理页面设计要点

1. **统一的项目列表**
   - 显示用户参与的所有项目
   - 清晰标识用户在每个项目中的角色
   - 根据角色权限显示不同的操作按钮

2. **角色权限标识**
   - PROJECT_OWNER：显示所有管理操作（编辑、删除、成员管理等）
   - PROJECT_ADMIN：显示管理操作（编辑、成员管理，但不能删除项目）
   - PROJECT_MEMBER：显示参与操作（查看、任务管理等）
   - PROJECT_VIEWER：只显示查看操作

3. **操作按钮设计**
   - 查看：所有角色都可见
   - 编辑：PROJECT_OWNER和PROJECT_ADMIN可见
   - 删除：只有PROJECT_OWNER可见
   - 成员管理：PROJECT_OWNER和PROJECT_ADMIN可见

### 菜单权限配置

通过Cool Admin现有的菜单管理系统配置：

1. **菜单结构创建**
   - 在菜单管理中创建项目工作台菜单树
   - 为每个菜单项配置对应的Vue组件路径
   - 设置菜单的显示顺序和图标

2. **角色权限分配**
   - 在角色管理中为四种项目角色分配菜单权限
   - 根据角色级别分配不同的操作权限
   - 配置按钮级别的权限控制

3. **动态权限加载**
   - 用户切换到项目维度时，系统根据用户的项目角色动态加载菜单
   - 前端通过permmenu接口获取用户有权限的菜单项
   - 实现菜单的实时权限控制